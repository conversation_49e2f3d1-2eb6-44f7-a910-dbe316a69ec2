import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { Link, useLoaderData } from "@remix-run/react";
import { useState } from "react";
import {
  Alert,
  AlertDescription,
  Avatar,
  AvatarFallback,
  AvatarImage,
  Button,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Input,
  Textarea,
} from "@repo/ui-kit";
import { 
  ArrowRight, 
  MessageSquare, 
  Send, 
  Settings, 
  Shield, 
  Star, 
  Users, 
  Zap,
  Bot,
  User,
  History,
  Plus
} from "lucide-react";
import { getUser } from "~/lib/auth.server";

export const meta: MetaFunction = () => {
  return [
    { title: "AI-Powered Platform - Modern, Fast, Secure" },
    {
      name: "description",
      content:
        "Experience the future of AI with our cutting-edge platform built on Remix, Cloudflare, and Neon",
    },
  ];
};

export async function loader({ request, context }: LoaderFunctionArgs) {
  const user = await getUser(request, context);

  return json({
    user,
  });
}

const features = [
  {
    icon: MessageSquare,
    title: "AI Chat",
    description: "Intelligent conversations powered by advanced AI models",
  },
  {
    icon: Zap,
    title: "Lightning Fast",
    description: "Built on Cloudflare Edge for global performance",
  },
  {
    icon: Shield,
    title: "Secure & Private",
    description: "Enterprise-grade security with Neon database",
  },
  {
    icon: Users,
    title: "Team Collaboration",
    description: "Work together seamlessly with your team",
  },
];

const testimonials = [
  {
    name: "Sarah Johnson",
    role: "Product Manager",
    company: "TechCorp",
    content:
      "This platform has revolutionized how we work with AI. The speed and reliability are unmatched.",
    avatar: "/images/avatars/sarah.svg",
  },
  {
    name: "Michael Chen",
    role: "CTO",
    company: "StartupXYZ",
    content:
      "The integration was seamless and the performance gains were immediate. Highly recommended!",
    avatar: "/images/avatars/mike.svg",
  },
];

export default function Index() {
  const { user } = useLoaderData<typeof loader>();
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: "assistant" as const,
      content: "Hello! I'm your AI assistant. How can I help you today?",
      timestamp: new Date(),
    },
  ]);
  const [inputMessage, setInputMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage = {
      id: messages.length + 1,
      type: "user" as const,
      content: inputMessage,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage("");
    setIsLoading(true);

    // Simulate AI response
    setTimeout(() => {
      const aiResponse = {
        id: messages.length + 2,
        type: "assistant" as const,
        content: "Thanks for your message! This is a demo response. In a real implementation, this would connect to your AI service.",
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, aiResponse]);
      setIsLoading(false);
    }, 1000);
  };

  return (
    <div className="min-h-screen bg-background flex">
      {/* Sidebar */}
      <aside className="w-80 border-r bg-muted/30 flex flex-col">
        {/* Sidebar Header */}
        <div className="p-4 border-b">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <MessageSquare className="w-5 h-5 text-primary-foreground" />
            </div>
            <h1 className="text-lg font-semibold">AI Platform</h1>
          </div>
          <Button className="w-full" size="sm">
            <Plus className="w-4 h-4 mr-2" />
            New Chat
          </Button>
        </div>

        {/* Chat History */}
        <div className="flex-1 overflow-y-auto p-4">
          <div className="space-y-3">
            <div className="text-sm text-muted-foreground mb-3 flex items-center">
              <History className="w-4 h-4 mr-2" />
              Recent Chats
            </div>
            <div className="space-y-2">
              <div className="p-3 rounded-lg bg-primary/10 border border-primary/20 cursor-pointer">
                <div className="text-sm font-medium mb-1">Current Chat</div>
                <div className="text-xs text-muted-foreground">AI Assistant conversation</div>
              </div>
              <div className="p-3 rounded-lg hover:bg-muted cursor-pointer">
                <div className="text-sm font-medium mb-1">Getting Started</div>
                <div className="text-xs text-muted-foreground">How to use the platform</div>
              </div>
              <div className="p-3 rounded-lg hover:bg-muted cursor-pointer">
                <div className="text-sm font-medium mb-1">Project Ideas</div>
                <div className="text-xs text-muted-foreground">Brainstorming session</div>
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar Footer */}
        <div className="p-4 border-t">
          <div className="flex items-center space-x-3 mb-3">
            {user ? (
              <>
                <Avatar className="w-8 h-8">
                  <AvatarFallback>{user.email?.charAt(0).toUpperCase()}</AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium truncate">{user.email}</div>
                  <div className="text-xs text-muted-foreground">Signed in</div>
                </div>
              </>
            ) : (
              <>
                <Avatar className="w-8 h-8">
                  <AvatarFallback>G</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="text-sm font-medium">Guest User</div>
                  <div className="text-xs text-muted-foreground">Not signed in</div>
                </div>
              </>
            )}
            <Button variant="ghost" size="sm">
              <Settings className="w-4 h-4" />
            </Button>
          </div>
          {!user && (
            <Link to="/login" className="block">
              <Button variant="outline" size="sm" className="w-full">
                Sign In
              </Button>
            </Link>
          )}
        </div>
      </aside>

      {/* Main Chat Area */}
      <main className="flex-1 flex flex-col">
        {/* Chat Header */}
        <header className="p-4 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Bot className="w-6 h-6 text-primary" />
              <div>
                <h2 className="text-lg font-semibold">AI Assistant</h2>
                <p className="text-sm text-muted-foreground">Always ready to help</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {user && (
                <Link to="/dashboard">
                  <Button variant="outline" size="sm">
                    Dashboard
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </header>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.type === "user" ? "justify-end" : "justify-start"}`}
            >
              <div
                className={`flex items-start space-x-3 max-w-2xl ${
                  message.type === "user" ? "flex-row-reverse space-x-reverse" : ""
                }`}
              >
                <Avatar className="w-8 h-8 flex-shrink-0">
                  {message.type === "user" ? (
                    <AvatarFallback>
                      <User className="w-4 h-4" />
                    </AvatarFallback>
                  ) : (
                    <AvatarFallback>
                      <Bot className="w-4 h-4" />
                    </AvatarFallback>
                  )}
                </Avatar>
                <div
                  className={`rounded-lg p-3 ${
                    message.type === "user"
                      ? "bg-primary text-primary-foreground"
                      : "bg-muted"
                  }`}
                >
                  <p className="text-sm">{message.content}</p>
                  <p className="text-xs opacity-70 mt-1">
                    {message.timestamp.toLocaleTimeString()}
                  </p>
                </div>
              </div>
            </div>
          ))}
          {isLoading && (
            <div className="flex justify-start">
              <div className="flex items-start space-x-3 max-w-2xl">
                <Avatar className="w-8 h-8 flex-shrink-0">
                  <AvatarFallback>
                    <Bot className="w-4 h-4" />
                  </AvatarFallback>
                </Avatar>
                <div className="rounded-lg p-3 bg-muted">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
                    <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Message Input */}
        <div className="p-4 border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="flex space-x-2 max-w-4xl mx-auto">
            <div className="flex-1">
              <Textarea
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                placeholder="Ask me anything..."
                className="min-h-[50px] max-h-32 resize-none"
                onKeyDown={(e) => {
                  if (e.key === "Enter" && !e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage();
                  }
                }}
              />
            </div>
            <Button
              onClick={handleSendMessage}
              disabled={!inputMessage.trim() || isLoading}
              size="sm"
              className="self-end h-[50px] px-4"
            >
              <Send className="w-4 h-4" />
            </Button>
          </div>
          <div className="text-center mt-2">
            <p className="text-xs text-muted-foreground">
              Press Enter to send, Shift+Enter for new line
            </p>
          </div>
        </div>
      </main>
    </div>
  );
}
